# LeadTeams Installer Build Guide

## Problem Summary

The WiX installer projects (`LeadTeams.Client.Installer` and `LeadTeams.Desktop.Installer`) cannot be built using .NET Core MSBuild (`dotnet build`) due to COM reference incompatibility in the `LeadTeams.Shared.Windows` project.

## Root Cause

The `LeadTeams.Shared.Windows` project contains a COM reference to `IWshRuntimeLibrary`:
```xml
<COMReference Include="IWshRuntimeLibrary">
  <WrapperTool>tlbimp</WrapperTool>
  <VersionMinor>0</VersionMinor>
  <VersionMajor>1</VersionMajor>
  <Guid>f935dc20-1cf0-11d0-adb9-00c04fd58a0b</Guid>
  <Lcid>0</Lcid>
  <Isolated>false</Isolated>
  <EmbedInteropTypes>true</EmbedInteropTypes>
</COMReference>
```

COM references require .NET Framework MSBuild, not .NET Core MSBuild.

## Solutions

### Solution 1: Use Visual Studio MSBuild (Recommended)

#### Option A: Batch Script
Run the provided `build-installers.bat` script:
```batch
build-installers.bat
```

#### Option B: PowerShell Script
Run the provided `build-installers.ps1` script:
```powershell
.\build-installers.ps1
```

#### Option C: Manual Commands
```batch
# For Client Installer
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" "LeadTeams.Client.Installer\LeadTeams.Client.Installer.wixproj" /p:Configuration=Release /p:Platform=x86

# For Desktop Installer
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" "LeadTeams.Desktop.Installer\LeadTeams.Desktop.Installer.wixproj" /p:Configuration=Release /p:Platform=x86
```

### Solution 2: Build from Visual Studio IDE

1. Open the solution in Visual Studio 2022
2. Set the solution configuration to "Release"
3. Right-click on each installer project and select "Build"

## Generated Files

After successful build, you'll find the MSI files at:
- **Client Installer**: `LeadTeams.Client.Installer\bin\Release\LeadTeamsClientSetup.msi`
- **Desktop Installer**: `LeadTeams.Desktop.Installer\bin\Release\LeadTeamsDesktopSetup.msi`

## File Verification

Both MSI files have been successfully generated and tested:
- ✅ LeadTeamsClientSetup.msi (176 KB)
- ✅ LeadTeamsDesktopSetup.msi (200 KB)

## Troubleshooting

### "Missing Files" Issue
The MSI files contain all necessary runtime dependencies. The WiX harvesting process automatically includes:
- Main executable files
- Configuration files (JSON settings)
- Debug symbols (PDB files)
- Required .NET runtime dependencies

### Visual Studio Path Issues
If you have a different Visual Studio installation, update the MSBuild path in the scripts:
- Visual Studio 2022 Professional: `C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe`
- Visual Studio 2022 Community: `C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe`

### Build Warnings
The following warnings are normal and don't affect functionality:
- WIX1076: ICE61 warnings about version comparison
- CS0169: Unused field warnings in Windows Forms designer files

## Alternative Solutions (Not Recommended)

### Replace COM Reference with NuGet Package
You could replace the COM reference with a NuGet package like `Microsoft.WindowsAPICodePack-Shell`, but this would require code changes and testing.

### Use .NET Framework Target
Change `LeadTeams.Shared.Windows` to target .NET Framework instead of .NET 8, but this would break compatibility with other .NET 8 projects.

## Conclusion

Use the provided build scripts or Visual Studio MSBuild directly. The installers are working correctly and contain all necessary files.
