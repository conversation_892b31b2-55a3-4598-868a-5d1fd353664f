@echo off
echo Building LeadTeams Installers...

REM Set the path to Visual Studio MSBuild
set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"

REM Check if MSBuild exists
if not exist %MSBUILD_PATH% (
    echo Error: Visual Studio MSBuild not found at %MSBUILD_PATH%
    echo Please install Visual Studio 2022 or update the path in this script.
    pause
    exit /b 1
)

echo.
echo Building LeadTeams Client Installer...
%MSBUILD_PATH% "LeadTeams.Client.Installer\LeadTeams.Client.Installer.wixproj" /p:Configuration=Release /p:Platform=x86 /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Error building Client Installer
    pause
    exit /b 1
)

echo.
echo Building LeadTeams Desktop Installer...
%MSBUILD_PATH% "LeadTeams.Desktop.Installer\LeadTeams.Desktop.Installer.wixproj" /p:Configuration=Release /p:Platform=x86 /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Error building Desktop Installer
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Client Installer: LeadTeams.Client.Installer\bin\Release\LeadTeamsClientSetup.msi
echo Desktop Installer: LeadTeams.Desktop.Installer\bin\Release\LeadTeamsDesktopSetup.msi
echo.
pause
