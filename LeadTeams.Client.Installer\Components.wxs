<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Fragment>

    <!-- Component group for all application files -->
    <ComponentGroup Id="ClientProductComponents" Directory="INSTALLFOLDER">

      <!-- Main executable -->
      <Component Id="ClientMainExecutable" Guid="{11111111-1111-1111-1111-111111111111}">
        <File Id="LeadTeamsClientExe"
              Source="$(var.LeadTeams.Client.TargetPath)"
              KeyPath="yes" />
      </Component>

      <!-- Application icon -->
      <Component Id="ClientApplicationIcon" Guid="{*************-2222-2222-************}">
        <File Id="ClientApplicationIconFile"
              Source="$(var.LeadTeams.Client.ProjectDir)icons8-monitoring-96.ico"
              KeyPath="yes" />
      </Component>

      <!-- Configuration files -->
      <Component Id="ClientConfigFiles" Guid="{12345678-1234-1234-1234-123456789ABC}">
        <File Id="ClientAppConfig"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.dll.config"
              KeyPath="yes" />
        <File Id="ClientRuntimeConfig"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.runtimeconfig.json" />
        <File Id="ClientDepsJson"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.deps.json" />
      </Component>

      <!-- Debug symbols (optional) -->
      <Component Id="ClientDebugSymbols" Guid="{*************-5555-5555-************}">
        <File Id="LeadTeamsClientPdb"
              Source="$(var.LeadTeams.Client.TargetDir)LeadTeams.Client.pdb"
              KeyPath="yes" />
      </Component>

      <!-- This will be expanded to include all necessary runtime dependencies -->
      <!-- Use Heat.exe to harvest all files from the output directory -->

    </ComponentGroup>

  </Fragment>
</Wix>
