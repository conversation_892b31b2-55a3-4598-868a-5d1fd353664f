# PowerShell script to build LeadTeams installers
Write-Host "Building LeadTeams Installers..." -ForegroundColor Green

# Set the path to Visual Studio MSBuild
$msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"

# Check if MSBuild exists
if (-not (Test-Path $msbuildPath)) {
    Write-Host "Error: Visual Studio MSBuild not found at $msbuildPath" -ForegroundColor Red
    Write-Host "Please install Visual Studio 2022 or update the path in this script." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    Write-Host "`nBuilding LeadTeams Client Installer..." -ForegroundColor Yellow
    & $msbuildPath "LeadTeams.Client.Installer\LeadTeams.Client.Installer.wixproj" /p:Configuration=Release /p:Platform=x86 /verbosity:minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Error building Client Installer"
    }

    Write-Host "`nBuilding LeadTeams Desktop Installer..." -ForegroundColor Yellow
    & $msbuildPath "LeadTeams.Desktop.Installer\LeadTeams.Desktop.Installer.wixproj" /p:Configuration=Release /p:Platform=x86 /verbosity:minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Error building Desktop Installer"
    }

    Write-Host "`nBuild completed successfully!" -ForegroundColor Green
    Write-Host "`nGenerated files:" -ForegroundColor Cyan
    Write-Host "Client Installer: LeadTeams.Client.Installer\bin\Release\LeadTeamsClientSetup.msi" -ForegroundColor White
    Write-Host "Desktop Installer: LeadTeams.Desktop.Installer\bin\Release\LeadTeamsDesktopSetup.msi" -ForegroundColor White
}
catch {
    Write-Host "`nError: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Read-Host "`nPress Enter to exit"
