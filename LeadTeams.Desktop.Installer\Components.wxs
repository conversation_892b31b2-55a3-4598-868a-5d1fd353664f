﻿<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Fragment>

    <!-- Component group for all application files -->
    <ComponentGroup Id="DesktopProductComponents" Directory="INSTALLFOLDER">

      <!-- Main executable -->
      <Component Id="DesktopMainExecutable" Guid="{*************-6666-6666-************}">
        <File Id="LeadTeamsDesktopExe" Source="$(var.LeadTeams.Desktop.TargetPath)" KeyPath="yes" />
      </Component>

      <!-- Application icon -->
      <Component Id="DesktopApplicationIcon" Guid="{*************-7777-7777-************}">
        <File Id="DesktopApplicationIconFile" Source="$(var.LeadTeams.Desktop.ProjectDir)icons8-monitoring-96.ico" KeyPath="yes" />
      </Component>

      <!-- Configuration files -->
      <Component Id="DesktopConfigFiles" Guid="{*************-4321-4321-CBA987654321}">
        <File Id="DesktopAppConfig" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.dll.config" KeyPath="yes" />
        <File Id="DesktopRuntimeConfig" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.runtimeconfig.json" />
        <File Id="DesktopDepsJson" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.deps.json" />
      </Component>

      <!-- Debug symbols (optional) -->
      <Component Id="DesktopDebugSymbols" Guid="{AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA}">
        <File Id="LeadTeamsDesktopPdb" Source="$(var.LeadTeams.Desktop.TargetDir)LeadTeams.Desktop.pdb" KeyPath="yes" />
      </Component>

      <!-- This will be expanded to include all necessary runtime dependencies -->
      <!-- Use Heat.exe to harvest all files from the output directory -->

    </ComponentGroup>

  </Fragment>
</Wix>
